import Client, { CommitmentLevel, SubscribeRequest } from "@triton-one/yellowstone-grpc";
import {
    SystemProgram,
    SystemInstruction,
    PublicKey,
    type ParsedTransactionWithMeta,
} from '@solana/web3.js';
import bs58 from 'bs58';
import { Pool } from 'pg';


// 数据库连接配置
const pool = new Pool({
    user: 'postgres',
    password: '46e8FJ0t0rTgfk0wqc',
    host: '*************',
    port: 5432,
    database: 'postgres',
});

// 数据库操作函数
async function insertTransferLog(signature: string, slot: number, fromAddress: string, toAddress: string, amount: number) {
    try {
        // 插入到 wallet_transfer_logs 表
        const query = `
            INSERT INTO wallet_transfer_logs (signature, slot, from_address, to_address, amount)
            VALUES ($1, $2, $3, $4, $5)
            ON CONFLICT DO NOTHING
            RETURNING id
        `;
        const result = await pool.query(query, [signature, slot, fromAddress, toAddress, amount]);

        // 同时插入到 wallet_second_filter 表
        if (result.rowCount && result.rowCount > 0) {
            const secondFilterQuery = `
                INSERT INTO wallet_second_filter (signature, slot, from_address, to_address, amount, status)
                VALUES ($1, $2, $3, $4, $5, 0)
                ON CONFLICT DO NOTHING
            `;
            await pool.query(secondFilterQuery, [signature, slot, fromAddress, toAddress, amount]);

            // console.log(`✅ 已写入数据库，ID: ${result.rows[0].id}`);
            return true;
        }
        return false;
    } catch (error) {
        console.error('❌ 数据库写入失败:', error);
        return false;
    }
}


// 工具函数：安全写入stream
async function writeToStream(stream: any, request: SubscribeRequest) {
    return new Promise<void>((resolve, reject) => {
        stream.write(request, (err: unknown) => {
            if (!err) resolve();
            else reject(err);
        });
    });
}
const dexList = [
"ASTyfSima4LLAdDgoFGkgqoKowG1LZFDr9fAQrg7iaJZ",
"AC5RDfQFmDS1deWZos921JfqscXdByf8BKHs5ACWjtW2",
"5tzFkiKscXHK5ZXCGbXZxdw7gTjjD1mBwuoFbhUvuAi9",
"is6MTRHEgyFLNTfYcuV4QBWLjrZBfmhVNYR6ccgr8KV",
"H8sMJSCQxfKiFTCfDR3DUMLPwcRbM61LGFJ8N4dK3WjS",
"GJRs4FwHtemZ5ZE9x3FNvJ8TMwitKTh21yxdRPqn7npE",
"2AQdpHJ2JpcEgPiATUXjQxA8QmafFegfQwSLWSprPicm",
"D89hHJT5Aqyx1trP6EnGY9jJUB3whgnq3aUvvCqedvzf",
"FpwQQhQQoEaVu3WU2qZMfF1hx48YyfwsLoRgXG83E99Q",
"BY4StcU9Y2BpgH8quZzorg31EGE4L1rjomN8FNsCBEcx",
"4NyK1AdJBNbgaJ9EsKz3J4rfeHsuYdjkTPg3JaNdLeFw",
"9obNtb5GyUegcs3a1CbBkLuc5hEWynWfJC6gjz5uWQkE",
"BmFdpraQhkiDQE6SnfG5omcA1VwzqfXrwtNYBwWTymy6",
"53unSgGWqEWANcPYRF35B2Bgf8BkszUtcccKiXwGGLyr",
"DQ5JWbJyWdJeyBxZuuyu36sUBud6L6wo3aN1QC1bRmsR",
"CSEncqtqbmNRjve42sNnbs5cCSmrjUNsAEwc17XY2RCs",
"5g7yNHyGLJ7fiQ9SN9mf47opDnMjc585kqXWt6d7aBWs",
"DPqsobysNf5iA9w7zrQM8HLzCKZEDMkZsWbiidsAt1xo",
"FWznbcNXWQuHTawe9RxvQ2LdCENssh12dsznf4RiouN5"
]




async function startStream() {
    const endpoint = "http://ny.vision-node.com:10000"
    const client = new Client(
        endpoint,
        undefined,
        {
            "grpc.max_receive_message_length": 5 * 1024 * 1024 * 1024,
        }
    );

    console.log("正在连接到 Solana 事件流...");

    const stream = await client.subscribe();

    const request: SubscribeRequest = {
        commitment: CommitmentLevel.PROCESSED,
        accountsDataSlice: [],
        ping: undefined,
        transactions: {
            client: {
                vote: false,
                failed: false,
                accountInclude: dexList,
                accountRequired: [],
                accountExclude: [],

            },
        },
        accounts: {},
        slots: {},
        transactionsStatus: {},
        entry: {},
        blocks: {},
        blocksMeta: {},
    };

    // 使用工具函数写入stream
    console.log("正在订阅 Solana 交易...");
    await writeToStream(stream, request);
    console.log("订阅成功，等待交易数据...");

    // 监听 stream 数据
    stream.on("data", async (rawTransactionData: any) => {
        try {

            const message = rawTransactionData.transaction?.transaction?.transaction?.message;
            // 获取交易前后的余额信息
            const preBalances = rawTransactionData.transaction?.transaction?.meta?.preBalances;
            const postBalances = rawTransactionData.transaction?.transaction?.meta?.postBalances;

            const instructions = message.instructions;
            const accountKeys = message.accountKeys;
            const signatures = bs58.encode(rawTransactionData.transaction.transaction.signature)
            for (const ix of instructions) {

                const programId = new PublicKey(accountKeys[ix.programIdIndex]);
                const accounts: number[] = Array.from(ix.accounts);
                const keys = accounts
                    .map((acctIndex) => {
                        const key = accountKeys[acctIndex];
                        if (!key) return null;
                        return {
                            pubkey: new PublicKey(key),
                            isSigner: false,
                            isWritable: true,
                        };
                    })
                    .filter((x): x is { pubkey: PublicKey; isSigner: boolean; isWritable: boolean } => x !== null);

                const data = Buffer.from(ix.data);

                // 检查是否是 SystemProgram 转账指令
                if (programId.equals(SystemProgram.programId) && data[0] === 2) {
                    const type = SystemInstruction.decodeInstructionType({ programId, data, keys });
                    if (type === 'Transfer') {
                        let transferInfo: ReturnType<typeof SystemInstruction.decodeTransfer> | undefined;
                        try {
                            transferInfo = SystemInstruction.decodeTransfer({ programId, data, keys });
                        } catch (e) {
                            console.warn("⚠️ 无法解析转账指令:", e);
                        }
                        // 打印转账信息
                        if (transferInfo?.fromPubkey && transferInfo?.toPubkey) {
                            // 过滤：转账和目标地址相同 或 金额低于0.0005 SOL
                            const from = transferInfo.fromPubkey.toBase58();
                            const to = transferInfo.toPubkey.toBase58();
                            const lamports = transferInfo.lamports;
                            const solAmount = Number(lamports) / 1e9;
                            if (from === to) return;
                            if (solAmount < 0.1) return;
                            if (solAmount > 100) return;

                            const slot = rawTransactionData.transaction.slot;
                            console.log("slot", slot);
                            console.log("signatures", signatures);
                            console.log(`💸 SOL 转账:`);
                            console.log(`From: ${transferInfo.fromPubkey.toBase58()}`);
                            console.log(`To: ${transferInfo.toPubkey.toBase58()}`);

                            if (solAmount > 0) {
                                console.log(`Amount: ${solAmount.toFixed(9)} SOL`);
                            } else {
                                console.log(`Amount: ${(lamports).toString()} lamports`);
                            }

                            // 判断接收方是否为新钱包
                            if (preBalances && postBalances && accountKeys) {
                                // 找到接收方地址在accountKeys中的索引
                                const toIndex = accountKeys.findIndex((key: any) =>
                                    new PublicKey(key).toBase58() === to
                                );

                                if (toIndex !== -1) {
                                    // 获取交易前后的余额
                                    const preLamports = preBalances[toIndex] || 0;
                                    const postLamports = postBalances[toIndex] || 0;

                                    // 转换为SOL显示
                                    const preBalance = preLamports / 1e9;
                                    const postBalance = postLamports / 1e9;

                                    // 如果交易前余额为0或接近0，则可能是新钱包
                                    // 考虑到可能有一些小额费用，使用0.001 SOL作为阈值
                                    const isZeroPreBalance = preBalance < 0.001;

                                    if (isZeroPreBalance) {
                                        console.log(`✨ 检测到新钱包! 地址: ${to}`);
                                        console.log(`   交易前余额: ${preBalance.toFixed(9)} SOL`);
                                        console.log(`   交易后余额: ${postBalance.toFixed(9)} SOL`);
                                        console.log(`   转账金额: ${solAmount.toFixed(9)} SOL`);

                                        // 只有新钱包才写入交易记录和目标地址
                                        await insertTransferLog(
                                            signatures,
                                            slot,
                                            transferInfo.fromPubkey.toBase58(),
                                            transferInfo.toPubkey.toBase58(),
                                            solAmount
                                        );

                                        console.log(`✅ 新钱包交易已记录: ${to}`);
                                    } else {
                                        console.log(`ℹ️ 非新钱包，跳过记录: ${to}`);
                                    }
                                }
                            }
                        } else {
                            console.log(signatures)
                            console.warn("⚠️ 转账信息缺失");
                        }
                    }
                }
            }
        } catch (err) {
            // console.error("解析失败:", err);
        }
    });

    // 错误监听
    stream.on("error", async (err: unknown) => {
        console.error("📛 Stream error, 尝试重连:", err);
        process.exit(1); // 新增退出命令
    });

    // 结束监听
    stream.on("end", async () => {
        console.warn("🔌 Stream 已关闭，尝试重连...");
        process.exit(1); // 新增退出命令
    });

    // 定时发送 ping 保持连接活跃
    const pingRequest: SubscribeRequest = {
        accounts: {},
        slots: {},
        transactions: {},
        transactionsStatus: {},
        blocks: {},
        blocksMeta: {},
        entry: {},
        accountsDataSlice: [],
        commitment: undefined,
        ping: { id: 1 },
    };

    setInterval(async () => {
        try {
            await writeToStream(stream, pingRequest);
        } catch (err) {
            console.error("Ping 失败:", err);
        }
    }, 2000);
}

// 测试数据库连接
async function testDatabaseConnection() {
    try {
        const client = await pool.connect();
        console.log('✅ 数据库连接成功');

        // 检查表是否存在
        const checkTableQuery = `
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = 'wallet_transfer_logs'
            );
        `;
        const result = await client.query(checkTableQuery);

        if (result.rows[0].exists) {
            console.log('✅ wallet_transfer_logs 表已存在');
        } else {
            console.error('❌ wallet_transfer_logs 表不存在，请先创建表');
            process.exit(1);
        }

        client.release();
        return true;
    } catch (error) {
        console.error('❌ 数据库连接失败:', error);
        return false;
    }
}

// 主函数，启动流
async function main() {
    // 测试数据库连接
    const dbConnected = await testDatabaseConnection();
    if (!dbConnected) {
        console.error('❌ 无法连接到数据库，程序退出');
        process.exit(1);
    }

    // 启动流监听
    await startStream();
}

// 处理程序退出
process.on('SIGINT', async () => {
    console.log('正在关闭数据库连接...');
    await pool.end();
    console.log('数据库连接已关闭')
    process.exit(0);
});

// 启动主程序
main().catch(err => {
    console.error('程序运行出错:', err);
    process.exit(1);
});
