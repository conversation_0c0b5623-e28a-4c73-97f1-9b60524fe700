#!/bin/bash

echo "🦀 Solana gRPC Monitor - Rust版本构建脚本"
echo "=========================================="

# 检查Rust是否安装
if ! command -v cargo &> /dev/null; then
    echo "❌ Cargo未找到，请先安装Rust"
    echo "访问 https://rustup.rs/ 安装Rust"
    exit 1
fi

echo "✅ Rust版本: $(rustc --version)"

# 选择构建模式
echo ""
echo "请选择构建模式:"
echo "1) 简单连接测试 (推荐用于诊断)"
echo "2) 简化版本 (无数据库，避免依赖冲突)"
echo "3) 调试版本 (详细日志)"
echo "4) 完整版本 (包含数据库)"

read -p "请输入选择 (1-4): " choice

case $choice in
    1)
        echo "🔧 构建简单连接测试..."
        cat > Cargo.toml << EOF
[package]
name = "solana-grpc-test"
version = "0.1.0"
edition = "2021"

[[bin]]
name = "simple_test"
path = "src/simple_test.rs"

[dependencies]
tokio = { version = "1.0", features = ["full"] }
yellowstone-grpc-client = "1.13"
yellowstone-grpc-proto = "1.13"
anyhow = "1.0"
tracing = "0.1"
tracing-subscriber = "0.3"
futures = "0.3"

[dependencies.zeroize]
version = "1.3"
EOF
        echo "🧹 清理旧构建..."
        cargo clean
        echo "📦 构建简单测试..."
        cargo build --bin simple_test
        if [ $? -eq 0 ]; then
            echo "✅ 构建成功!"
            echo "运行测试: cargo run --bin simple_test"
        fi
        ;;
    2)
        echo "🔧 构建简化版本 (推荐)..."
        cp Cargo_minimal.toml Cargo.toml
        cat >> Cargo.toml << EOF

[[bin]]
name = "minimal"
path = "src/minimal_main.rs"
EOF
        echo "🧹 清理旧构建..."
        cargo clean
        echo "📦 构建简化版本..."
        cargo build --bin minimal
        if [ $? -eq 0 ]; then
            echo "✅ 构建成功!"
            echo "运行程序: cargo run --bin minimal"
        else
            echo "❌ 构建失败，尝试更新依赖..."
            cargo update
            cargo build --bin minimal
        fi
        ;;
    3)
        echo "🔧 构建调试版本..."
        cp Cargo_debug.toml Cargo.toml
        echo "🧹 清理旧构建..."
        cargo clean
        echo "📦 构建调试版本..."
        cargo build --bin debug
        if [ $? -eq 0 ]; then
            echo "✅ 构建成功!"
            echo "运行调试: cargo run --bin debug"
        else
            echo "❌ 构建失败，尝试更新依赖..."
            cargo update
            cargo build --bin debug
        fi
        ;;
    4)
        echo "🔧 构建完整版本..."
        echo "⚠️  注意：可能遇到依赖冲突"
        echo "🧹 清理旧构建..."
        cargo clean
        echo "📦 构建完整版本..."
        cargo build
        if [ $? -eq 0 ]; then
            echo "✅ 构建成功!"
            echo "运行程序: cargo run"
        else
            echo "❌ 构建失败，建议使用简化版本 (选项2)"
            echo "或者尝试: cargo update && cargo build"
        fi
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo ""
echo "🔍 故障排除提示:"
echo "- 如果编译失败，尝试: cargo clean && cargo build"
echo "- 如果依赖问题，尝试: cargo update"
echo "- 如果网络问题，检查防火墙和代理设置"
echo "- 查看 README.md 获取更多帮助"
